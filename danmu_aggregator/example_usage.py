#!/usr/bin/env python3
"""
弹幕聚合服务使用示例
"""

import httpx
import asyncio
import json


async def test_danmu_aggregator():
    """测试弹幕聚合服务的各个功能"""
    base_url = "http://localhost:7769"

    async with httpx.AsyncClient(timeout=60.0) as client:
        print("🚀 开始测试弹幕聚合服务...")
        
        # 1. 测试根路径
        print("\n1. 测试根路径...")
        response = await client.get(f"{base_url}/")
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        
        # 2. 测试弹幕API可用性
        print("\n2. 测试弹幕API可用性...")
        try:
            response = await client.get(f"{base_url}/api/test")
            result = response.json()
            print(f"状态码: {response.status_code}")
            for api_name, api_result in result["test_results"].items():
                status = api_result["status"]
                url = api_result["url"]
                if status == "success":
                    count = api_result["comment_count"]
                    print(f"✅ {api_name}: 成功 ({count}条弹幕) - {url}")
                elif status == "no_data":
                    print(f"⚠️  {api_name}: 无数据 - {url}")
                else:
                    error = api_result.get("error", "未知错误")
                    print(f"❌ {api_name}: 失败 ({error}) - {url}")
        except Exception as e:
            print(f"❌ 弹幕API测试失败: {e}")
        
        # 3. 测试搜索功能
        print("\n3. 测试搜索功能...")
        search_params = {"anime": "demon slayer"}
        response = await client.get(f"{base_url}/api/test/search/episodes", params=search_params)
        result = response.json()
        print(f"状态码: {response.status_code}")
        print(f"搜索结果: 找到 {len(result.get('animes', []))} 个作品")
        if result.get("errorMessage"):
            print(f"消息: {result['errorMessage']}")
        
        # 4. 测试外部弹幕获取
        print("\n4. 测试外部弹幕获取...")
        test_urls = [
            "https://www.bilibili.com/video/BV1xx411c7mu",  # 经典蓝蓝路视频
            "https://www.bilibili.com/bangumi/play/ep123456"  # 测试URL
        ]
        
        for url in test_urls:
            print(f"\n测试URL: {url}")
            params = {"url": url}
            response = await client.get(f"{base_url}/api/test/extcomment", params=params)
            result = response.json()
            print(f"状态码: {response.status_code}")
            print(f"弹幕数量: {result.get('count', 0)}")
            
            # 显示前3条弹幕作为示例
            comments = result.get("comments", [])
            if comments:
                print("示例弹幕:")
                for i, comment in enumerate(comments[:3]):
                    time_info = comment["p"].split(",")[0]
                    content = comment["m"]
                    print(f"  {i+1}. [{time_info}s] {content}")
                if len(comments) > 3:
                    print(f"  ... 还有 {len(comments) - 3} 条弹幕")
        
        print("\n✅ 测试完成！")


if __name__ == "__main__":
    asyncio.run(test_danmu_aggregator())
