# 弹幕聚合服务

这是一个独立的弹幕聚合子项目，提供与主项目兼容的API接口，通过聚合多个弹幕源来获取弹幕数据。

## 功能特性

- 🔍 **影视搜索**: 支持通过影视名称搜索多个平台的内容
- 🎯 **弹幕聚合**: 聚合5个弹幕API的数据，提供更丰富的弹幕内容
- 🔄 **格式转换**: 自动转换JSON和XML格式的弹幕为dandanplay格式
- 🚀 **高性能**: 并发请求多个API，快速响应
- 📱 **兼容性**: 完全兼容dandanplay API格式

## 支持的弹幕源

1. `https://api.danmu.icu/?ac=dm&url=` (JSON格式)
2. `https://dmku.hls.one/?ac=dm&url=` (JSON格式)
3. `http://dm.apptotv.top/?ac=dm&url=` (JSON格式)
4. `https://danmu.56uxi.com/?ac=dm&url=` (JSON格式)
5. `https://fc.lyz05.cn/?url=` (XML格式)

## 安装和运行

### 1. 安装依赖

```bash
cd danmu_aggregator
pip install -r requirements.txt
```

### 2. 启动服务

```bash
# 方法1: 直接运行
python -m danmu_aggregator.main

# 方法2: 使用启动脚本
python run.py

# 方法3: 使用uvicorn
uvicorn danmu_aggregator.main:app --host 0.0.0.0 --port 7769 --reload
```

服务将在 `http://localhost:7769` 启动

## API接口

### 1. 搜索影视作品

```
GET /api/{token}/search/episodes?anime={影视名称}&episode={集数}
```

**参数:**
- `token`: 访问令牌 (可以是任意字符串)
- `anime`: 影视作品名称
- `episode`: 可选，指定集数

**示例:**
```bash
curl "http://localhost:7769/api/test/search/episodes?anime=鬼灭之刃"
```

### 2. 获取弹幕 (通过分集ID)

```
GET /api/{token}/comment/{episode_id}?chConvert={转换选项}
```

**参数:**
- `token`: 访问令牌
- `episode_id`: 分集ID (从搜索结果中获取)
- `chConvert`: 可选，中文简繁转换 (0-不转换，1-简体，2-繁体)

### 3. 获取外部弹幕 (通过URL)

```
GET /api/{token}/extcomment?url={视频URL}&chConvert={转换选项}
```

**参数:**
- `token`: 访问令牌
- `url`: 视频播放地址
- `chConvert`: 可选，中文简繁转换

**示例:**
```bash
curl "http://localhost:7769/api/test/extcomment?url=https://www.bilibili.com/bangumi/play/ep123456"
```

### 4. 测试弹幕API

```
GET /api/test?test_url={测试URL}
```

用于测试所有弹幕API的可用性。

## 配置

可以通过修改 `config.py` 或设置环境变量来配置服务:

```python
# 服务器配置
SERVER__HOST=0.0.0.0
SERVER__PORT=7769

# 代理配置 (可选)
PROXY_URL=http://proxy.example.com:8080

# 请求超时
REQUEST_TIMEOUT=30
```

## 使用流程

1. **搜索影视**: 客户端提供影视名称，服务搜索各平台获取播放地址
2. **获取弹幕**: 使用播放地址调用5个弹幕API获取弹幕数据
3. **格式转换**: 将不同格式的弹幕统一转换为dandanplay格式
4. **数据聚合**: 合并多个源的弹幕，去重并按时间排序
5. **返回结果**: 以标准格式返回给客户端

## 注意事项

- 本服务独立于主项目运行，不需要数据库
- 弹幕数据实时获取，不进行本地缓存
- 支持的视频平台取决于弹幕API的支持范围
- 建议在生产环境中配置适当的代理和超时设置

## 开发和扩展

如需添加更多弹幕源或搜索平台，可以:

1. 在 `config.py` 中添加新的API地址
2. 在 `search_service.py` 中添加新的搜索平台支持
3. 在 `danmu_converter.py` 中添加新的格式转换逻辑
