import httpx
import re
import json
import logging
from typing import List, Optional, Dict, Any
from bs4 import BeautifulSoup
from .models import SearchResult, EpisodeInfo
from .config import settings

logger = logging.getLogger(__name__)


class SearchService:
    """影视搜索服务"""
    
    def __init__(self):
        self.client: Optional[httpx.AsyncClient] = None
    
    async def _get_client(self) -> httpx.AsyncClient:
        """获取HTTP客户端"""
        if self.client is None:
            proxies = {"http://": settings.proxy_url, "https://": settings.proxy_url} if settings.proxy_url else None
            self.client = httpx.AsyncClient(
                timeout=settings.request_timeout,
                proxies=proxies,
                headers={
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
                }
            )
        return self.client
    
    async def close(self):
        """关闭HTTP客户端"""
        if self.client:
            await self.client.aclose()
            self.client = None
    
    async def search_bilibili(self, keyword: str) -> List[SearchResult]:
        """搜索B站内容"""
        client = await self._get_client()
        results = []
        
        try:
            # 搜索番剧
            url = "https://api.bilibili.com/x/web-interface/search/type"
            params = {
                "search_type": "media_bangumi",
                "keyword": keyword,
                "page": 1
            }
            
            response = await client.get(url, params=params)
            response.raise_for_status()
            data = response.json()
            
            if data.get("code") == 0 and data.get("data", {}).get("result"):
                for item in data["data"]["result"][:5]:  # 限制结果数量
                    # 提取season_id
                    season_id = None
                    if item.get("season_id"):
                        season_id = str(item["season_id"])
                    elif item.get("url"):
                        match = re.search(r"ss(\d+)", item["url"])
                        if match:
                            season_id = match.group(1)
                    
                    if season_id:
                        results.append(SearchResult(
                            title=item.get("title", "").replace("<em class=\"keyword\">", "").replace("</em>", ""),
                            url=f"https://www.bilibili.com/bangumi/play/ss{season_id}",
                            year=None,
                            episode_count=None,
                            image_url=item.get("cover"),
                            provider="bilibili",
                            media_id=f"ss{season_id}"
                        ))
            
        except Exception as e:
            logger.error(f"搜索B站内容失败: {e}")
        
        return results
    
    async def search_iqiyi(self, keyword: str) -> List[SearchResult]:
        """搜索爱奇艺内容"""
        client = await self._get_client()
        results = []
        
        try:
            url = "https://mesh.if.iqiyi.com/portal/lw/search/homePageV3"
            params = {
                'key': keyword, 'current_page': '1', 'mode': '1', 'source': 'input',
                'pageNum': '1', 'pageSize': '10'
            }
            
            response = await client.get(url, params=params)
            response.raise_for_status()
            data = response.json()
            
            if data.get("code") == "A00000" and data.get("data", {}).get("docinfos"):
                for item in data["data"]["docinfos"][:5]:
                    if item.get("albumDocInfo"):
                        album = item["albumDocInfo"]
                        # 提取link_id
                        link_id = None
                        if album.get("albumLink"):
                            match = re.search(r"v_(\w+)\.html", album["albumLink"])
                            if match:
                                link_id = match.group(1)
                        
                        if link_id:
                            results.append(SearchResult(
                                title=album.get("albumTitle", ""),
                                url=album.get("albumLink", ""),
                                year=album.get("year"),
                                episode_count=album.get("totalVideoCount"),
                                image_url=album.get("albumImg"),
                                provider="iqiyi",
                                media_id=link_id
                            ))
            
        except Exception as e:
            logger.error(f"搜索爱奇艺内容失败: {e}")
        
        return results
    
    async def search_all(self, keyword: str) -> List[SearchResult]:
        """搜索所有平台"""
        all_results = []
        
        # 并发搜索各个平台
        import asyncio
        tasks = [
            self.search_bilibili(keyword),
            self.search_iqiyi(keyword),
        ]
        
        results_list = await asyncio.gather(*tasks, return_exceptions=True)
        
        for results in results_list:
            if isinstance(results, list):
                all_results.extend(results)
            elif isinstance(results, Exception):
                logger.error(f"搜索平台时出错: {results}")
        
        return all_results
    
    async def get_episodes(self, search_result: SearchResult) -> List[EpisodeInfo]:
        """获取分集信息"""
        if search_result.provider == "bilibili":
            return await self._get_bilibili_episodes(search_result)
        elif search_result.provider == "iqiyi":
            return await self._get_iqiyi_episodes(search_result)
        return []
    
    async def _get_bilibili_episodes(self, search_result: SearchResult) -> List[EpisodeInfo]:
        """获取B站分集信息"""
        client = await self._get_client()
        episodes = []
        
        try:
            # 获取番剧详情页面
            response = await client.get(search_result.url)
            response.raise_for_status()
            
            # 解析页面获取分集信息
            match = re.search(r'__INITIAL_STATE__=({.*?});', response.text)
            if match:
                initial_state = json.loads(match.group(1))
                ep_list = initial_state.get("epList", [])
                
                for i, ep in enumerate(ep_list):
                    episodes.append(EpisodeInfo(
                        episode_id=str(ep.get("id", i)),
                        title=ep.get("longTitle", f"第{i+1}集"),
                        url=f"https://www.bilibili.com/bangumi/play/ep{ep.get('id', i)}",
                        episode_index=i + 1
                    ))
        
        except Exception as e:
            logger.error(f"获取B站分集信息失败: {e}")
        
        return episodes
    
    async def _get_iqiyi_episodes(self, search_result: SearchResult) -> List[EpisodeInfo]:
        """获取爱奇艺分集信息"""
        # 简化实现，返回单集
        return [EpisodeInfo(
            episode_id=search_result.media_id,
            title="第1集",
            url=search_result.url,
            episode_index=1
        )]
