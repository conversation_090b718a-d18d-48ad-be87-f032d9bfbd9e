import httpx
import json
import logging
import asyncio
from typing import List, Optional, Dict, Any
from .models import Comment, CommentResponse
from .danmu_converter import DanmuConverter
from .config import settings

logger = logging.getLogger(__name__)


class DanmuService:
    """弹幕聚合服务"""
    
    def __init__(self):
        self.client: Optional[httpx.AsyncClient] = None
        self.converter = DanmuConverter()
    
    async def _get_client(self) -> httpx.AsyncClient:
        """获取HTTP客户端"""
        if self.client is None:
            proxies = {"http://": settings.proxy_url, "https://": settings.proxy_url} if settings.proxy_url else None
            self.client = httpx.AsyncClient(
                timeout=settings.request_timeout,
                proxies=proxies,
                headers={
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
                }
            )
        return self.client
    
    async def close(self):
        """关闭HTTP客户端"""
        if self.client:
            await self.client.aclose()
            self.client = None
    
    async def _fetch_danmu_from_api(self, api_url: str, video_url: str) -> Optional[List[Comment]]:
        """从单个弹幕API获取弹幕数据"""
        client = await self._get_client()
        
        try:
            # 构造完整的API请求URL
            full_url = f"{api_url}{video_url}"
            logger.info(f"正在请求弹幕API: {full_url}")
            
            response = await client.get(full_url)
            response.raise_for_status()
            
            # 判断响应格式
            content_type = response.headers.get("content-type", "").lower()
            
            if "application/json" in content_type or full_url.startswith("https://fc.lyz05.cn"):
                # JSON格式 (前4个API)
                try:
                    data = response.json()
                    return self.converter.convert_json_to_dandanplay(data)
                except json.JSONDecodeError:
                    logger.error(f"JSON解析失败: {full_url}")
                    return None
            else:
                # XML格式 (最后一个API)
                try:
                    return self.converter.convert_xml_to_dandanplay(response.text)
                except Exception as e:
                    logger.error(f"XML解析失败: {e}")
                    return None
                    
        except httpx.TimeoutException:
            logger.warning(f"弹幕API请求超时: {api_url}")
            return None
        except httpx.HTTPStatusError as e:
            logger.warning(f"弹幕API请求失败 {e.response.status_code}: {api_url}")
            return None
        except Exception as e:
            logger.error(f"弹幕API请求异常: {e}")
            return None
    
    async def aggregate_danmu(self, video_url: str) -> CommentResponse:
        """
        聚合所有弹幕API的数据
        
        Args:
            video_url: 视频播放地址
            
        Returns:
            CommentResponse: 聚合后的弹幕数据
        """
        logger.info(f"开始聚合弹幕，视频URL: {video_url}")
        
        # 并发请求所有弹幕API
        tasks = []
        for api_url in settings.danmu_apis:
            task = self._fetch_danmu_from_api(api_url, video_url)
            tasks.append(task)
        
        # 等待所有请求完成
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 收集有效的弹幕数据
        valid_comments = []
        for i, result in enumerate(results):
            if isinstance(result, list) and result:
                logger.info(f"API {i+1} 返回了 {len(result)} 条弹幕")
                valid_comments.append(result)
            elif isinstance(result, Exception):
                logger.error(f"API {i+1} 请求异常: {result}")
            else:
                logger.warning(f"API {i+1} 未返回有效弹幕数据")
        
        if not valid_comments:
            logger.warning("所有弹幕API都未返回有效数据")
            return CommentResponse(count=0, comments=[])
        
        # 合并和去重弹幕
        merged_comments = self.converter.merge_comments(valid_comments)
        
        logger.info(f"弹幕聚合完成，共获得 {len(merged_comments)} 条弹幕")
        
        return CommentResponse(
            count=len(merged_comments),
            comments=merged_comments
        )
    
    async def get_danmu_by_episode_url(self, episode_url: str) -> CommentResponse:
        """
        根据分集URL获取弹幕
        
        Args:
            episode_url: 分集播放地址
            
        Returns:
            CommentResponse: 弹幕数据
        """
        return await self.aggregate_danmu(episode_url)
    
    async def test_apis(self, test_url: str = "https://www.bilibili.com/bangumi/play/ep123456") -> Dict[str, Any]:
        """
        测试所有弹幕API的可用性
        
        Args:
            test_url: 测试用的视频URL
            
        Returns:
            Dict: 测试结果
        """
        results = {}
        
        for i, api_url in enumerate(settings.danmu_apis):
            api_name = f"API_{i+1}"
            try:
                comments = await self._fetch_danmu_from_api(api_url, test_url)
                if comments:
                    results[api_name] = {
                        "status": "success",
                        "url": api_url,
                        "comment_count": len(comments),
                        "sample_comment": comments[0].m if comments else None
                    }
                else:
                    results[api_name] = {
                        "status": "no_data",
                        "url": api_url,
                        "comment_count": 0
                    }
            except Exception as e:
                results[api_name] = {
                    "status": "error",
                    "url": api_url,
                    "error": str(e)
                }
        
        return results
