import uvicorn
import logging
from contextlib import asynccontextmanager
from fastapi import FastAPI, Query, Path, HTTPException, Depends
from fastapi.responses import JSONResponse
from typing import Optional, List
import re

from .config import settings
from .models import (
    DandanResponseBase, DandanSearchEpisodesResponse, DandanAnimeInfo, 
    DandanEpisodeInfo, CommentResponse, SearchResult
)
from .search_service import SearchService
from .danmu_service import DanmuService

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 全局服务实例
search_service: Optional[SearchService] = None
danmu_service: Optional[DanmuService] = None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    global search_service, danmu_service
    
    # 启动时初始化服务
    search_service = SearchService()
    danmu_service = DanmuService()
    logger.info("弹幕聚合服务启动完成")
    
    yield
    
    # 关闭时清理资源
    if search_service:
        await search_service.close()
    if danmu_service:
        await danmu_service.close()
    logger.info("弹幕聚合服务关闭完成")


app = FastAPI(
    title="弹幕聚合API",
    description="基于dandanplay API风格的弹幕聚合服务",
    version="1.0.0",
    lifespan=lifespan
)


def get_search_service() -> SearchService:
    """获取搜索服务实例"""
    if search_service is None:
        raise HTTPException(status_code=500, detail="搜索服务未初始化")
    return search_service


def get_danmu_service() -> DanmuService:
    """获取弹幕服务实例"""
    if danmu_service is None:
        raise HTTPException(status_code=500, detail="弹幕服务未初始化")
    return danmu_service


def parse_search_keyword(keyword: str) -> dict:
    """解析搜索关键词，提取标题、季数和集数"""
    # 匹配 "标题 SXXEXX" 格式
    pattern = r'^(.+?)\s+[Ss](\d+)[Ee](\d+)$'
    match = re.match(pattern, keyword.strip())
    
    if match:
        return {
            "title": match.group(1).strip(),
            "season": int(match.group(2)),
            "episode": int(match.group(3))
        }
    
    return {
        "title": keyword.strip(),
        "season": None,
        "episode": None
    }


@app.get("/", include_in_schema=False)
async def root():
    """根路径"""
    return {"message": "弹幕聚合API服务", "version": "1.0.0"}


@app.get("/api/test")
async def test_apis(
    test_url: str = Query("https://www.bilibili.com/bangumi/play/ep123456", description="测试用的视频URL"),
    danmu_svc: DanmuService = Depends(get_danmu_service)
):
    """测试所有弹幕API的可用性"""
    results = await danmu_svc.test_apis(test_url)
    return {"test_results": results}


@app.get("/api/{token}/search/episodes", response_model=DandanSearchEpisodesResponse)
async def search_episodes(
    token: str = Path(..., description="访问令牌"),
    anime: str = Query(..., description="节目名称"),
    episode: Optional[str] = Query(None, description="分集标题 (通常是数字)"),
    search_svc: SearchService = Depends(get_search_service)
):
    """
    搜索影视作品和分集信息
    兼容dandanplay的/api/v2/search/episodes接口
    """
    logger.info(f"搜索请求: anime='{anime}', episode='{episode}'")
    
    try:
        # 解析搜索关键词
        parsed = parse_search_keyword(anime)
        search_title = parsed["title"]
        
        # 搜索影视作品
        search_results = await search_svc.search_all(search_title)
        
        if not search_results:
            return DandanSearchEpisodesResponse(
                success=True,
                animes=[],
                errorMessage=f"未找到与 '{search_title}' 相关的内容"
            )
        
        # 转换为dandanplay格式
        animes = []
        for i, result in enumerate(search_results[:10]):  # 限制结果数量
            # 获取分集信息
            episodes_info = await search_svc.get_episodes(result)
            
            # 转换分集格式
            episodes = []
            for ep in episodes_info:
                episodes.append(DandanEpisodeInfo(
                    episodeId=hash(f"{result.provider}_{result.media_id}_{ep.episode_id}") % **********,
                    episodeTitle=ep.title
                ))
            
            # 如果没有分集信息，创建一个默认分集
            if not episodes:
                episodes.append(DandanEpisodeInfo(
                    episodeId=hash(f"{result.provider}_{result.media_id}_1") % **********,
                    episodeTitle="第1集"
                ))
            
            animes.append(DandanAnimeInfo(
                animeId=hash(f"{result.provider}_{result.media_id}") % **********,
                animeTitle=result.title,
                imageUrl=result.image_url or "",
                searchKeyword=search_title,
                type="tv" if result.episode_count and result.episode_count > 1 else "movie",
                typeDescription="电视剧" if result.episode_count and result.episode_count > 1 else "电影",
                episodes=episodes
            ))
        
        return DandanSearchEpisodesResponse(
            success=True,
            animes=animes
        )
        
    except Exception as e:
        logger.error(f"搜索失败: {e}", exc_info=True)
        return DandanSearchEpisodesResponse(
            success=False,
            errorCode=500,
            errorMessage=f"搜索失败: {str(e)}",
            animes=[]
        )


@app.get("/api/{token}/comment/{episode_id}", response_model=CommentResponse)
async def get_comments(
    token: str = Path(..., description="访问令牌"),
    episode_id: int = Path(..., description="分集ID"),
    chConvert: int = Query(0, description="中文简繁转换。0-不转换，1-转换为简体，2-转换为繁体。"),
    search_svc: SearchService = Depends(get_search_service),
    danmu_svc: DanmuService = Depends(get_danmu_service)
):
    """
    获取指定分集的弹幕
    兼容dandanplay的弹幕获取接口
    """
    logger.info(f"获取弹幕请求: episode_id={episode_id}")
    
    try:
        # 这里需要根据episode_id反推出原始的搜索结果和播放URL
        # 由于这是一个简化的实现，我们使用一个示例URL
        # 在实际应用中，你可能需要维护一个episode_id到URL的映射
        
        # 示例：使用B站的一个测试URL
        test_url = "https://www.bilibili.com/bangumi/play/ep123456"
        
        # 获取弹幕数据
        comments_response = await danmu_svc.aggregate_danmu(test_url)
        
        return comments_response
        
    except Exception as e:
        logger.error(f"获取弹幕失败: {e}", exc_info=True)
        return CommentResponse(count=0, comments=[])


@app.get("/api/{token}/extcomment")
async def get_external_comments(
    token: str = Path(..., description="访问令牌"),
    url: str = Query(..., description="外部视频链接"),
    chConvert: int = Query(0, description="中文简繁转换。0-不转换，1-转换为简体，2-转换为繁体。"),
    danmu_svc: DanmuService = Depends(get_danmu_service)
):
    """
    从外部URL获取弹幕
    兼容dandanplay的外部弹幕接口
    """
    logger.info(f"外部弹幕请求: url='{url}'")
    
    try:
        # 直接使用提供的URL获取弹幕
        comments_response = await danmu_svc.aggregate_danmu(url)
        return comments_response
        
    except Exception as e:
        logger.error(f"获取外部弹幕失败: {e}", exc_info=True)
        return CommentResponse(count=0, comments=[])


if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host=settings.server.host,
        port=settings.server.port,
        reload=True
    )
