import json
import xml.etree.ElementTree as ET
from typing import List, Dict, Any, Optional
import re
import logging
from models import Comment

logger = logging.getLogger(__name__)


class DanmuConverter:
    """弹幕格式转换器"""
    
    @staticmethod
    def convert_json_to_dandanplay(json_data: Dict[str, Any]) -> List[Comment]:
        """
        将JSON格式的弹幕数据转换为dandanplay格式
        
        JSON格式示例:
        {
          "code": 23,
          "name": "3294b5e681699c00a4556e92aaf46078",
          "danum": 3,
          "danmuku": [
            [0, "top", "#ff00d0", "", "有6650条弹幕列队来袭~做好准备吧！", "", "", "24px"],
            [61, "right", "#ff006f", "", "红红火火恍恍惚惚", "", "", "24px"]
          ]
        }
        """
        comments = []
        danmuku_list = json_data.get("danmuku", [])
        
        for i, danmu in enumerate(danmuku_list):
            if len(danmu) < 5:
                continue
                
            time = danmu[0]  # 时间（秒）
            position = danmu[1]  # 位置：top, bottom, right
            color = danmu[2]  # 颜色
            content = danmu[4]  # 弹幕内容
            
            # 转换位置类型
            mode = 1  # 默认滚动弹幕
            if position == "top":
                mode = 5  # 顶部固定
            elif position == "bottom":
                mode = 4  # 底部固定
            
            # 转换颜色格式（从#rrggbb到数字）
            color_int = 16777215  # 默认白色
            if color and color.startswith("#"):
                try:
                    color_int = int(color[1:], 16)
                except ValueError:
                    pass
            
            # 构造p参数：时间,模式,字号,颜色,时间戳,池,用户ID,弹幕ID
            p = f"{time},{mode},25,{color_int},0,0,0,{i}"
            
            comments.append(Comment(
                cid=i,
                p=p,
                m=content
            ))
        
        return comments
    
    @staticmethod
    def convert_xml_to_dandanplay(xml_content: str) -> List[Comment]:
        """
        将XML格式的弹幕数据转换为dandanplay格式
        
        XML格式示例:
        <?xml version="1.0" encoding="utf-8"?>
        <i>
        <d p="0,1,25,16777215,1755835991,0,0,26732601000067074,1">在北极挖掘机机器。</d>
        <d p="0.58,1,25,16777215,1755835991,0,0,26732601000067074,1">我12岁了</d>
        </i>
        """
        comments = []
        
        try:
            root = ET.fromstring(xml_content)
            
            for i, d_elem in enumerate(root.findall('d')):
                p_attr = d_elem.get('p', '')
                content = d_elem.text or ''
                
                if not p_attr or not content:
                    continue
                
                # p属性格式：时间,模式,字号,颜色,时间戳,池,用户ID,弹幕ID,权重
                # 我们保持原有格式，只修改弹幕ID
                p_parts = p_attr.split(',')
                if len(p_parts) >= 4:
                    # 更新弹幕ID为序号
                    if len(p_parts) >= 8:
                        p_parts[7] = str(i)
                    p = ','.join(p_parts)
                else:
                    p = p_attr
                
                comments.append(Comment(
                    cid=i,
                    p=p,
                    m=content
                ))
        
        except ET.ParseError as e:
            logger.error(f"XML解析错误: {e}")
            return []
        
        return comments
    
    @staticmethod
    def merge_comments(comment_lists: List[List[Comment]]) -> List[Comment]:
        """
        合并多个弹幕列表，去重并按时间排序
        """
        all_comments = []
        seen_content = set()
        
        for comments in comment_lists:
            for comment in comments:
                # 简单去重：基于弹幕内容
                if comment.m not in seen_content:
                    seen_content.add(comment.m)
                    all_comments.append(comment)
        
        # 按时间排序
        def get_time(comment: Comment) -> float:
            try:
                p_parts = comment.p.split(',')
                return float(p_parts[0]) if p_parts else 0.0
            except (ValueError, IndexError):
                return 0.0
        
        all_comments.sort(key=get_time)
        
        # 重新分配ID
        for i, comment in enumerate(all_comments):
            comment.cid = i
            # 更新p参数中的弹幕ID
            p_parts = comment.p.split(',')
            if len(p_parts) >= 8:
                p_parts[7] = str(i)
                comment.p = ','.join(p_parts)
        
        return all_comments
